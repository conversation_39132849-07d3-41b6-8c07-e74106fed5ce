import Header from '@/components/Header';
import Sidebar from '@/components/Sidebar';
import { ProConfigProvider, WaterMark } from '@ant-design/pro-components';
import { Outlet, history, useIntl, useModel } from '@umijs/max';
import { App, ConfigProvider, Dropdown, Tabs } from 'antd';
import classNames from 'classnames';
import dayjs from 'dayjs';
import type { ItemType, MenuInfo } from 'rc-menu/lib/interface';
import { useEffect, useMemo } from 'react';
import { AliveScope } from 'react-activation';
import type { KeepAliveTab } from './useKeepAliveTabs';
import { useKeepAliveTabs } from './useKeepAliveTabs';
import { useMatchRoute } from './useMatchRoute';


type MenuItemType = (ItemType & { key: OperationType }) | null;

export enum OperationType {
  REFRESH = 'refresh',
  CLOSE = 'close',
  CLOSEOTHER = 'close-other',
}

export default function BaseLayout() {
  const { isSidebarFold, keepAliveTabs, setKeepAliveTabs, activeTabRoutePath, setActiveTabRoutePath } = useModel('layoutModel');
  const { initialState } = useModel('@@initialState');
  const { querySaleColumns } = useModel('saleModel');
  const intl = useIntl()
  const matchRoute = useMatchRoute();

  useEffect(() => {
    querySaleColumns();
  }, []);


  useEffect(() => {
    console.log('matchRoute', matchRoute);
    if (!matchRoute) return;

    const existKeepAliveTab = keepAliveTabs.find((o) => o.routePath === matchRoute?.routePath);

    setActiveTabRoutePath(matchRoute.routePath);

    // 如果不存在则需要插入
    if (!existKeepAliveTab) {
      setKeepAliveTabs((prev) => [
        ...prev,
        {
          title: matchRoute.title,
          key: matchRoute.routePath,
          routePath: matchRoute.routePath,
          pathname: matchRoute.pathname,
          search: matchRoute.search,
          icon: matchRoute.icon,
        },
      ]);
    } else if (existKeepAliveTab.pathname !== matchRoute.pathname) {
      // 如果是同一个路由，但是参数不同，我们只需要刷新当前页签并且把pathname设置为新的pathname， children设置为新的children
      setKeepAliveTabs((prev) => {
        const index = prev.findIndex((tab) => tab.routePath === matchRoute.routePath);

        if (index >= 0) {
          prev[index].pathname = matchRoute.pathname;
        }

        return [...prev];
      });
    } else {
      // 如果pathname相同但是search不同
      if (
        existKeepAliveTab.pathname === matchRoute.pathname &&
        existKeepAliveTab.search !== matchRoute.search
      ) {
        setKeepAliveTabs((prev) => {
          const index = prev.findIndex((tab) => tab.routePath === matchRoute.routePath);
          if (index >= 0) {
            prev[index].search = matchRoute.search;
          }
          return [...prev];
        });
      }
    }
  }, [matchRoute]);

  const {
    closeTab,
    refreshTab,
    closeOtherTab,
  } = useKeepAliveTabs();


  const menuItems: MenuItemType[] = useMemo(
    () =>
      [
        {
          label: intl.formatMessage({ id: 'layout.tabActions.refresh' }),
          key: OperationType.REFRESH,
        },
        keepAliveTabs.length <= 1
          ? null
          : {
            label: intl.formatMessage({ id: 'layout.tabActions.close' }),
            key: OperationType.CLOSE,
          },
        keepAliveTabs.length <= 1
          ? null
          : {
            label: intl.formatMessage({ id: 'layout.tabActions.closeOther' }),
            key: OperationType.CLOSEOTHER,
          },
      ].filter((o) => o),
    [keepAliveTabs],
  );

  const menuClick = ({ key, domEvent }: MenuInfo, tab: KeepAliveTab) => {
    if (key === OperationType.REFRESH) {
      refreshTab(tab.routePath);
    } else if (key === OperationType.CLOSE) {
      closeTab(tab.routePath);
    } else if (key === OperationType.CLOSEOTHER) {
      closeOtherTab(tab.routePath);
    }
  };

  const renderTabTitle = (tab: KeepAliveTab) => {
    return (
      <Dropdown
        menu={{ items: menuItems, onClick: (e) => menuClick(e, tab) }}
        trigger={['contextMenu']}
      >
        <div style={{ margin: '-12px 0', padding: '12px 0' }}>{tab.title}</div>
      </Dropdown>
    );
  };

  const tabItems = () => {
    return keepAliveTabs.map((tab) => {
      return {
        key: tab.routePath,
        label: renderTabTitle(tab),
        closable: keepAliveTabs.length > 1,
      };
    });
  };

  const onTabsChange = (tabRoutePath: string) => {
    const curTab = keepAliveTabs.find((o) => o.routePath === tabRoutePath);
    if (curTab) {
      history.push(`${curTab?.pathname}${curTab?.search}`);
    }
  };

  const onTabEdit = (
    targetKey: React.MouseEvent | React.KeyboardEvent | string,
    action: 'add' | 'remove',
  ) => {
    if (action === 'remove') {
      closeTab(targetKey as string);
    }
  };

  return (
    <ConfigProvider
      theme={{
        token: {
          colorPrimary: '#F49C1F',
          borderRadius: 2,
        },
        components: {
          Menu: {
            collapsedWidth: 48,
          },
          Button: {
            defaultBorderColor: '#F49C1F',
            defaultColor: '#F49C1F',
            defaultHoverBg: '#fffbed',
            defaultActiveBg: '#fffbed'
          },
          Table: {
            headerBg: '#F5F5F5',
            cellPaddingBlock: 10,
            // cellPaddingBlockMD: 4,
            // cellPaddingBlockSM: 4,
            // cellPaddingInline: 4,
            // cellPaddingInlineMD: 4,
            // cellPaddingInlineSM: 4,
          },
          Pagination: {
            borderRadius: 2,
          },
          Form: {
            itemMarginBottom: 16,
          },
          Descriptions: {
            itemPaddingBottom: 8,
          },
        },
      }}
    >
      <AliveScope>
        <App>
          <ProConfigProvider token={{}}>
            <WaterMark
              fontColor="rgba(0, 0 ,0, 0.1)"
              zIndex={9999}
              content={`${initialState?.currentUser?.accountName} ${dayjs().format(
                'YYYY-MM-DD HH:mm:ss',
              )}`}
            >
              <div className="pt-[56px]">
                <div className="fixed left-0 top-0 right-0 z-50 h-[56px]">
                  <Header />
                </div>
                <div>
                  <div
                    className={classNames(
                      'fixed left-0 top-[56px] bottom-0 bg-white transition-all',
                      {
                        'w-[180px]': !isSidebarFold,
                        'w-[48px]': isSidebarFold,
                      },
                    )}
                  >
                    <Sidebar />
                  </div>
                  <div
                    className={classNames('min-h-[calc(100vh-56px)] transition-all', {
                      'ml-[180px]': !isSidebarFold,
                      'ml-[48px]': isSidebarFold,
                    })}
                  >
                    <Tabs
                      type="editable-card"
                      items={tabItems()}
                      activeKey={activeTabRoutePath}
                      onChange={onTabsChange}
                      hideAdd
                      animated={false}
                      onEdit={onTabEdit}
                      tabBarStyle={{ background: '#fff', marginBottom: 0 }}
                    />
                    <Outlet />
                  </div>
                </div>
              </div>
            </WaterMark>
          </ProConfigProvider>
        </App>
      </AliveScope>
    </ConfigProvider>
  );
}
