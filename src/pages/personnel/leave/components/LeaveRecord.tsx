import FunProTable from "@/components/common/FunProTable";
import { TimeRangeFormat } from "@/components/common/TimeFormat";
import { ProColumns } from "@ant-design/pro-components";
import { useIntl, useModel } from "@umijs/max";
import { useBoolean } from "ahooks";
import { Button, message, Modal, Space } from "antd";
import dayjs from "dayjs";
import { useRef, useState } from "react";
import { accountListQuerySimple, queryStoreByAccount } from "../../user/services";
import { cancelLeaveApply, queryLeaveList } from "../services";
import { LeaveEntity } from "../types/leave.entity";
import { LeaveExchangeChannel, LeaveStateEnum, LeaveStateOptions, LeaveTypeOptions } from "../types/leave.enum";
import ApplyLeaveModal from "./ApplyLeaveModal";
import ApproveLeaveModal from "./ApproveLeaveModal";
import PayLeaveModal from "./PayLeaveModal";



const LeaveRecord = () => {
  const intl = useIntl();
  const t = (key: string) => intl.formatMessage({ id: key });
  const [modal, contextHolder] = Modal.useModal();
  const { initialState } = useModel('@@initialState');
  const { currentUser } = initialState;

  const [applyLeaveModalVisible, { setTrue: showApplyLeaveModal, setFalse: hideApplyLeaveModal }] = useBoolean(false);
  const [payLeaveModalVisible, { setTrue: showPayLeaveModal, setFalse: hidePayLeaveModal }] = useBoolean(false);
  const [approveLeaveModalVisible, { setTrue: showApproveLeaveModal, setFalse: hideApproveLeaveModal }] = useBoolean(false);

  const [selectedLeave, setSelectedLeave] = useState<any>(null);

  const actionRef = useRef<any>();

  const handleApplyLeave = () => {
    setSelectedLeave(null);
    showApplyLeaveModal();
  };

  const handleEditApplyLeave = (leave: LeaveEntity) => {
    if (leave.exchangeChannel === LeaveExchangeChannel.Leave) {
      showApplyLeaveModal();
    }
    setSelectedLeave({ ...leave, type: String(leave.type) });
  };

  const handlePayLeave = () => {
    showPayLeaveModal();
  };

  const handleApproveLeave = (record: any) => {
    setSelectedLeave({ ...record, type: String(record.type) });
    showApproveLeaveModal();
  };

  const reload = () => {
    actionRef.current?.reload();
  }

  const handleCancelLeave = async (record: any, action: string) => {
    const confirmed = await modal.confirm({
      title: 'Confirm',
      content: action === 'withdraw' ? intl.formatMessage({ id: 'personnel.leave.message.leaveCancelConfirm' }) : intl.formatMessage({ id: 'personnel.leave.message.leaveVoidConfirm' }),
      okText: intl.formatMessage({ id: `common.button.${action}` }),
    });
    if (confirmed) {
      cancelLeaveApply({ id: record.id }).then((result) => {
        if (result) {
          message.success(intl.formatMessage({ id: 'common.message.operationSuccess' }));
          actionRef.current?.reload();
        }
      });
    }
  };

  const columns: ProColumns<LeaveEntity>[] = [
    {
      title: t('common.column.index'),
      valueType: 'index',
      width: 50,
      dataIndex: 'index',
    },
    {
      title: t('personnel.leave.column.store'),
      dataIndex: 'storeNames',
      width: 120,
      valueType: 'select',
      fieldProps: {
        mode: 'multiple',
        maxTagCount: 3,
        showSearch: true,
      },
      formItemProps: {
        name: 'storeIdList',
      },
      request: async () => {
        const data = await queryStoreByAccount({});
        return data?.map(({ id, name }) => ({
          value: id,
          label: name,
        }));
      },
    },
    {
      title: t('personnel.leave.leaveType'),
      dataIndex: 'type',
      valueEnum: LeaveTypeOptions,
      formItemProps: {
        name: 'typeList',
      },
      fieldProps: {
        mode: 'multiple',
        maxTagCount: 'responsive',
      },
    },
    {
      title: t('personnel.leave.employee'),
      dataIndex: 'accountName',
      key: 'memberName',
      valueType: 'select',
      fieldProps: {
        showSearch: true,
        fieldNames: { label: 'name', value: 'id' },
      },
      request: () => accountListQuerySimple({}),
      formItemProps: {
        name: 'accountId',
      },
    },
    { title: t('personnel.leave.reason'), dataIndex: 'reason', search: false },
    {
      title: t('personnel.leave.leaveTime'),
      dataIndex: 'startTime',
      render: (_, record) => {
        return <TimeRangeFormat shwoTime startTime={record.startTime || ''} endTime={record.endTime || ''} />;
      },
      valueType: 'dateRange',
      search: {
        transform: (value: any) => {
          return {
            startTime: value[0] ? dayjs(value[0]).format('YYYY-MM-DD 00:00:00') : undefined,
            endTime: value[1] ? dayjs(value[1]).format('YYYY-MM-DD 23:59:59') : undefined,
          };
        },
      },
    },
    {
      title: t('personnel.leave.column.recordType'),
      dataIndex: 'exchangeChannelDesc',
      search: false,
    },
    { title: t('personnel.leave.hours'), dataIndex: 'hours', search: false },
    { title: t('personnel.leave.column.submitter'), dataIndex: 'submitAccountName', search: false },
    {
      title: t('personnel.leave.column.approvalResult'), dataIndex: 'state', valueEnum: LeaveStateOptions,
      formItemProps: {
        name: 'stateList',
      },
      fieldProps: {
        mode: 'multiple',
        maxTagCount: 'responsive',
      },
    },
    { title: t('personnel.leave.column.approver'), dataIndex: 'approveAccountName', search: false },
    { title: t('personnel.leave.column.approveTime'), dataIndex: 'approveTime', search: false },
    {
      title: t('common.column.operation'),
      key: 'action',
      search: false,
      fixed: 'right',
      render: (_, record) => (
        <Space>
          {record.state === LeaveStateEnum.Pending_Review && (
            <a onClick={() => handleApproveLeave(record)}>{t('personnel.leave.approve')}</a>
          )}
          {[LeaveStateEnum.Pending_Review, LeaveStateEnum.Rejected].includes(
            record.state as LeaveStateEnum,
          ) && currentUser.accountId === record.accountId && <a onClick={() => handleEditApplyLeave(record)}>{t('common.button.edit')}</a>}
          {record.state === LeaveStateEnum.Pending_Review && <a onClick={() => handleCancelLeave(record, 'withdraw')}>{t('personnel.leave.action.withdraw')}</a>}
          {record.state === LeaveStateEnum.Approved && <a onClick={() => handleCancelLeave(record, 'void')}>{t('personnel.leave.action.void')}</a>}
        </Space >
      ),
    },
  ];
  return <>
    <FunProTable<LeaveEntity, any>
      actionRef={actionRef}
      columns={columns}
      requestPage={queryLeaveList}
      scroll={{ x: 'max-content' }}
      headerTitle={
        <Space>
          <Button type="primary" onClick={handleApplyLeave}>
            {t('personnel.leave.applyLeave')}
          </Button>
          <Button type="primary" ghost onClick={handlePayLeave}>
            {t('personnel.leave.payLeave')}
          </Button>
        </Space>
      }
    />
    {applyLeaveModalVisible && (
      <ApplyLeaveModal
        visible={applyLeaveModalVisible}
        onClose={hideApplyLeaveModal}
        onSuccess={reload}
        leave={selectedLeave}
      />
    )}
    {payLeaveModalVisible && (
      <PayLeaveModal
        visible={payLeaveModalVisible}
        onClose={hidePayLeaveModal}
        onSuccess={reload}
        leave={selectedLeave}
      />
    )}
    {approveLeaveModalVisible && (
      <ApproveLeaveModal
        visible={approveLeaveModalVisible}
        onClose={hideApproveLeaveModal}
        onSuccess={reload}
        leave={selectedLeave}
      />
    )}
    {contextHolder}
  </>
};

export default LeaveRecord;