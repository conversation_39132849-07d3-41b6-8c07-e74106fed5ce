import { BPointType } from '@/pages/sales/order/detail/types/bpoint.type';

export interface CreatePaymentRequest {
  /**
   * 金额（单位：元）
   */
  amount?: number;
  /**
   * 业务单号
   */
  businessNo?: string;
  /**
   * 支付类型@seecom.ipms.finance.account.api.dto.enums.BPointPayTypeEnums
   */
  payType?: BPointType;
  /**
   * 重定向地址
   */
  redirectionUrl?: string;
  /**
   * 备注
   */
  remark?: string;
  /**
   *
   * 收款门店ID（app商城：订单信息中的销售门店，PC工作台：选择的收款门店）V1：后端需要通过该字段查询其对应的销售主体（原先定的是在门店管理中新增销售主体字段）V2：门店管理不维护，后端配置写死，比如：123门店对应销售主体A，4门店对应销售主体B，5门店对应销售主体C需要保存客户信息时，该值必传
   */
  storeId?: string;
  /**
   * 客户ID
   */
  customerId?: string;
  /**
   * 业务类型
   */
  bizType?: PaymentBizType;
  /**
   * 销售主体/收款主体
   */
  saleOrgId?: string;
}

export enum PaymentBizType {
  Sale = 1,
  FinanceReceive,
}
