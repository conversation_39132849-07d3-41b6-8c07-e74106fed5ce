import { queryStoreByAccount } from '@/pages/personnel/user/services';
import { ModalForm, ProForm, ProFormDigit, ProFormSelect, ProFormText } from '@ant-design/pro-components';
import { message } from 'antd';
import React, { useEffect } from 'react';
import { useIntl } from 'react-intl';
import type { PurchaseRequestEntity } from '../../types/purchaseRequest.entity';
import { editPurchaseRequestDetail } from '../services';

export interface EditRequestModalProps {
  visible: boolean;
  onCancel: () => void;
  onSuccess: () => void;
  record?: PurchaseRequestEntity;
}

const EditRequestModal: React.FC<EditRequestModalProps> = ({
  visible,
  onCancel,
  onSuccess,
  record,
}) => {
  const intl = useIntl();
  const [form] = ProForm.useForm();

  useEffect(() => {
    if (visible && record) {
      form.setFieldsValue({
        sourceNo: record.sourceNo,
        storeId: record.storeId,
        storeName: record.storeName,
        warehouseName: record.warehouseName,
        supplierName: record.supplierName,
        itemName: record.itemName,
        price: record.price,
        num: record.num,
        remark: record.remark,
      });
    }
  }, [visible, record, form]);

  const handleSubmit = async (values: any) => {
    if (!record?.id) return;
    const result = await editPurchaseRequestDetail({
      id: record.id,
      sourceNo: record.sourceNo || '',
      purchaseNo: record.purchaseNo || '',
      storeId: values.storeId,
      warehouseId: record.warehouseId || '',
      supplierId: record.supplierId || '',
      itemSn: record.itemSn || '',
      num: values.num,
      price: values.price,
      remark: values.remark,
    });

    if (result) {
      message.success(intl.formatMessage({ id: 'common.message.save.success' }));
      onSuccess();
      onCancel();
    }

  };

  return (
    <ModalForm
      title={intl.formatMessage({ id: 'purchase.request.edit.modal.title' })}
      open={visible}
      width={600}
      layout="horizontal"
      labelCol={{ span: 6 }}
      wrapperCol={{ span: 16 }}
      form={form}
      onFinish={handleSubmit}
      onVisibleChange={onCancel}
    >
      <ProFormText
        name="sourceNo"
        label={intl.formatMessage({ id: 'purchase.request.list.columns.sourceNo' })}
        readonly
      />

      <ProFormSelect
        name="storeId"
        label={intl.formatMessage({ id: 'purchase.request.list.columns.storeName' })}
        rules={[
          {
            required: true,
          },
        ]}
        request={async () => {
          const data = await queryStoreByAccount({});
          return data?.map(({ id, name }) => ({
            value: id,
            label: name,
          })) || [];
        }}
      />

      <ProFormText
        name="warehouseName"
        label={intl.formatMessage({ id: 'purchase.request.list.columns.warehouseName' })}
        readonly
      />

      <ProFormText
        name="supplierName"
        label={intl.formatMessage({ id: 'purchase.request.list.columns.supplierName' })}
        readonly
      />

      <ProFormText
        name="itemName"
        label={intl.formatMessage({ id: 'purchase.request.list.columns.itemName' })}
        readonly
      />

      <ProFormDigit
        name="price"
        label={intl.formatMessage({ id: 'purchase.request.list.columns.price' })}
        rules={[
          {
            required: true,
          },
        ]}
        fieldProps={{
          precision: 2,
          min: 0.01,
          step: 0.01,
        }}
      />

      <ProFormDigit
        name="num"
        label={intl.formatMessage({ id: 'purchase.request.list.columns.num' })}
        rules={[
          {
            required: true,
          },
        ]}
        fieldProps={{
          precision: 0,
          min: 1,
          step: 1,
        }}
      />
      <ProFormText
        name="remark"
        label={intl.formatMessage({ id: 'purchase.request.list.columns.remark' })}
      />
    </ModalForm >
  );
};

export default EditRequestModal;
