import { PageResponseDataType } from '@/types/PageResponseDataType';
import { request } from '@/utils/request';
import { PurchaseRequestEntity } from '../types/purchaseRequest.entity';
import { SavePurchaseOrderRequest } from '../types/savePurchaseOrder';

export const queryPurchaseRequestList = async (
  params: Partial<PurchaseRequestEntity>,
): Promise<PageResponseDataType<PurchaseRequestEntity>> => {
  return request(`/ipmspurchase/purchaseRequestDetail/pageQuery`, {
    data: params,
  });
};

export const cancelPurchaseRequest = async (params: { id: number }): Promise<boolean> => {
  return request(`/ipmspurchase/purchaseRequestDetail/cancelPurchaseRequest`, {
    data: params,
  });
};

// 根据需求单生成采购单
export const savePurchaseOrderByRequest = async (
  params: SavePurchaseOrderRequest,
): Promise<{ orderNo: string; id: number }> => {
  return request(`/ipmspurchase/purchaseRequestDetail/savePurchaseOrderByRequest`, {
    data: params,
  });
};

export const editPurchaseRequestDetail = async (params: {
  id: number;
  sourceNo: string;
  purchaseNo: string;
  storeId: string;
  warehouseId: string;
  supplierId: string;
  itemSn: string;
  num: number;
  price: number;
  remark: string;
}): Promise<boolean> => {
  return request(
    `/ipmspurchase/purchaseRequestDetail/editPurchaseRequestDetail`,
    {
      data: params,
    },
  );
};
