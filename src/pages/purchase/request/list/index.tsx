import FunProTable from '@/components/common/FunProTable';
import withKeep<PERSON>live from '@/wrappers/withKeepAlive';
import type { ActionType, ProFormInstance } from '@ant-design/pro-components';
import { PageContainer } from '@ant-design/pro-components';
import { history, useIntl } from '@umijs/max';
import { App, Button, message } from 'antd';
import React, { useRef, useState } from 'react';
import type { PurchaseRequestEntity } from '../types/purchaseRequest.entity';
import EditRequestModal from './components/EditRequestModal';
import { PostListTableColumns } from './config/postListTableColumns';
import {
  cancelPurchaseRequest,
  queryPurchaseRequestList,
  savePurchaseOrderByRequest,
} from './services';

const PurchaseRequestList: React.FC = () => {
  const intl = useIntl();
  const actionRef = useRef<ActionType>();
  const formRef = useRef<ProFormInstance>();
  const [loading, setLoading] = useState(false);
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const [selectedRows, setSelectedRows] = useState<PurchaseRequestEntity[]>([]);
  const [editModalVisible, setEditModalVisible] = useState(false);
  const [editRecord, setEditRecord] = useState<PurchaseRequestEntity>();
  const { modal } = App.useApp();

  // 验证选中的需求单是否可以合并
  const validateSelectedRequests = (requests: PurchaseRequestEntity[]): boolean => {
    if (requests.length === 0) {
      message.warning(intl.formatMessage({ id: 'purchase.request.list.message.selectAtLeastOne' }));
      return false;
    }

    // 检查是否都是已提交状态
    const hasNonSubmittedStatus = requests.some((item) => item.status !== 'NEW');
    if (hasNonSubmittedStatus) {
      message.warning(
        intl.formatMessage({ id: 'purchase.request.list.message.onlySubmittedStatus' }),
      );
      return false;
    }

    // 检查是否同一采购门店、入库仓库、供应商
    const firstRequest = requests[0];
    const hasDifferentStore = requests.some(
      (item) =>
        item.storeId !== firstRequest.storeId ||
        item.warehouseId !== firstRequest.warehouseId ||
        item.supplierId !== firstRequest.supplierId,
    );

    if (hasDifferentStore) {
      message.warning(intl.formatMessage({ id: 'purchase.request.list.message.samePurchaseInfo' }));
      return false;
    }

    return true;
  };

  // 生成采购单
  const handleGeneratePurchaseOrder = async () => {
    if (!validateSelectedRequests(selectedRows)) {
      return;
    }

    modal.confirm({
      title: intl.formatMessage({ id: 'purchase.request.list.confirm.generateOrder' }),
      content: intl.formatMessage({ id: 'purchase.request.list.confirm.generateOrder.content' }),
      onOk: async () => {
        try {
          setLoading(true);
          const purchaseOrderList = selectedRows.map((record) => ({
            id: record.id,
            requestNo: record.requestNo,
            itemSn: record.itemSn,
            itemName: record.itemName,
            num: record.num,
            price: record.price,
            storeId: record.storeId,
            storeName: record.storeName,
            supplierId: record.supplierId,
            supplierName: record.supplierName,
            warehouseId: record.warehouseId,
            remark: record.remark,
          }));

          const result = await savePurchaseOrderByRequest({
            purchaseOrderList,
          });

          if (result?.id) {
            message.success(
              intl.formatMessage({ id: 'purchase.request.list.message.generateSuccess' }),
            );
            // 清空选择
            setSelectedRowKeys([]);
            setSelectedRows([]);
            // 刷新列表
            actionRef.current?.reload();
            // 跳转到采购单编辑页面
            history.push(`/purchase/external?purchaseOrderId=${result?.id}`);
          }
        } catch (error) {
          message.error(intl.formatMessage({ id: 'purchase.request.list.message.generateError' }));
        } finally {
          setLoading(false);
        }
      },
    });
  };

  const refresh = () => {
    setSelectedRowKeys([]);
    setSelectedRows([]);
    actionRef.current?.reload();
  };

  const handleCancelPurchaseRequest = async (record: PurchaseRequestEntity) => {
    if (!record.id) return;
    const result = await cancelPurchaseRequest({ id: record.id });
    if (result) {
      message.success(intl.formatMessage({ id: 'purchase.request.list.message.cancelSuccess' }));
      refresh();
    }
  };

  // 编辑需求单
  const handleEditRequest = (record: PurchaseRequestEntity) => {
    setEditRecord(record);
    setEditModalVisible(true);
  };



  return (
    <PageContainer>
      <FunProTable<PurchaseRequestEntity, any>
        rowKey="id"
        scroll={{ x: 'max-content' }}
        actionRef={actionRef}
        formRef={formRef}
        requestPage={(params) => {
          setSelectedRowKeys([]);
          setSelectedRows([]);
          return queryPurchaseRequestList(params)
        }}
        rowSelection={{
          selectedRowKeys,
          onChange: (keys, rows) => {
            setSelectedRowKeys(keys);
            setSelectedRows(rows);
          },
        }}
        headerTitle={
          <Button type="primary" loading={loading} onClick={handleGeneratePurchaseOrder}>
            {intl.formatMessage({ id: 'purchase.request.list.button.generateOrder' })}
          </Button>
        }
        columns={PostListTableColumns({
          handleCancelPurchaseRequest,
          handleEditRequest,
          loading,
          intl,
        })}
      />
      <EditRequestModal
        visible={editModalVisible}
        onCancel={() => setEditModalVisible(false)}
        onSuccess={refresh}
        record={editRecord}
      />
    </PageContainer>
  );
};

export default withKeepAlive(PurchaseRequestList);
