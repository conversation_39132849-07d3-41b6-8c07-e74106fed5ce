import AuthButton from '@/components/common/AuthButton';
import FunProTable from '@/components/common/FunProTable';
import { exportData } from '@/utils/exportData';
import withKeepAlive from '@/wrappers/withKeepAlive';
import { ColumnsState, ProFormInstance } from '@ant-design/pro-components';
import { PageContainer } from '@ant-design/pro-components';
import { type ActionType } from '@ant-design/pro-table/lib';
import { useIntl } from '@umijs/max';
import { Space } from 'antd';
import React, { useEffect, useRef, useState } from 'react';
import { useActivate } from 'react-activation';
import GoodsCreateDrawerForm from './components/GoodsCreateDrawerForm';
import GoodsImportModal from './components/GoodsImportModal';
import { PostListTableColumns } from './config/GoodsTableColumns';
import { queryGoodsPage, updateGoodsStatusBatch } from './services';
import { type GoodsCreateDrawerFormType } from './types/GoodsCreateDrawerFormType';
import { type GoodsEntity } from './types/GoodsEntity.entity';
import { type GoodsImportModalType } from './types/GoodsImportModalType';
import GeneralGroupCreateDrawer, {
  GeneralGroupCreateDrawerProps,
} from './components/GeneralGroupCreateDrawer';
import { querySysPropertyList, setSysProperty } from '@/services/systerm';
import { ConfigType } from '@/pages/system/config/components/types/ConfigType';
import GoodsDetailDrawer, { GoodsDetailDrawerProps } from '@/components/GoodsDetailDrawer';

const GoodsList = () => {
  const intl = useIntl();
  const actionRef = useRef<ActionType>();
  const formRef = useRef<ProFormInstance>();
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  // 新增商品
  const [createModalProps, setCreateModalProps] = useState<GoodsCreateDrawerFormType>({
    visible: false,
    recordId: 0,
    readOnly: false,
    title: intl.formatMessage({ id: 'goods.list.createGoodsTitle' }),
    onCancel: () => null,
  });
  // 新增通用组
  const [createGeneralGroupModalProps, setCreateGeneralGroupModalProps] =
    useState<GeneralGroupCreateDrawerProps>({
      visible: false,
    });
  // 查看商品
  const [goodsDrawer, setGoodsDrawer] = useState<GoodsDetailDrawerProps>({
    visible: false,
  });

  // 导入商品
  const [importModalProps, setImportModalProps] = useState<GoodsImportModalType>({
    visible: false,
  });

  const [columnsStateValue, setColumnsStateValue] = useState<Record<string, ColumnsState>>({});

  useActivate(() => {
    actionRef.current?.reload();
  });

  useEffect(() => {
    querySaleColumns();
  }, []);

  /**
   * 关闭【新增商品】对话框
   */
  const hideCreateModal = (reload: boolean) => {
    setCreateModalProps((preModalProps) => ({
      ...preModalProps,
      visible: false,
      recordId: 0,
      readOnly: false,
    }));
    if (!reload) return;
    setTimeout(() => {
      actionRef.current?.reload(reload);
    }, 1500);
  };

  /**
   * 关闭【导入商品】对话框
   */
  const hideImportModal = () => {
    setImportModalProps((preModalProps) => ({
      ...preModalProps,
      visible: false,
    }));
  };

  /**
   * 上架/下架
   * @param id
   */
  const handleUpOrDownItem = async (ids: React.Key[], status: number) => {
    const result = await updateGoodsStatusBatch({ itemIdList: ids, itemStatus: status });
    if (result) {
      setTimeout(() => {
        actionRef.current?.reload(true);
      }, 1500);
      setSelectedRowKeys([]);
    }
  };

  /**
   * 编辑
   * @param id
   */
  const handleUpdateItem = async (id: string) => {
    setCreateModalProps((preModalProps) => ({
      ...preModalProps,
      visible: true,
      readOnly: false,
      recordId: id,
      title: intl.formatMessage({ id: 'goods.list.editGoodsTitle' }),
    }));
  };

  const querySaleColumns = () => {
    querySysPropertyList({
      type: ConfigType.GoodsManageColumn,
      propDimensions: 'ACCOUNT',
    }).then((result) => {
      const columnValue =
        // @ts-ignore
        result?.filter((result) => result.type === ConfigType.GoodsManageColumn)[0]?.value || '{}';
      try {
        setColumnsStateValue(JSON.parse(columnValue));
      } catch (error) {
        console.log(error);
      }
    });
  };

  // @ts-ignore
  const onColumnsStatChange = (data) => {
    setColumnsStateValue(data);
    setSysProperty({
      propDimensions: 'ACCOUNT',
      type: ConfigType.GoodsManageColumn,
      value: data,
    });
  };

  return (
    <PageContainer>
      <FunProTable<GoodsEntity, any>
        rowKey="itemId"
        // @ts-ignore
        requestPage={queryGoodsPage}
        formRef={formRef}
        actionRef={actionRef}
        rowSelection={{
          selectedRowKeys: selectedRowKeys,
          onChange: (selectedKeys) => {
            setSelectedRowKeys(selectedKeys);
          },
        }}
        search={{
          layout: 'vertical',
          defaultCollapsed: false,
        }}
        columns={PostListTableColumns({
          handleUpdateItem,
          handleUpOrDownItem,
          setGoodsDrawer,
        })}
        columnsState={{
          value: columnsStateValue,
          onChange: onColumnsStatChange,
        }}
        headerTitle={
          <Space>
            <AuthButton
              type="primary"
              key="create"
              authority="addGoods"
              onClick={() => {
                setCreateModalProps((preModalProps) => ({
                  ...preModalProps,
                  visible: true,
                  readOnly: false,
                  title: intl.formatMessage({ id: 'goods.list.createGoodsTitle' }),
                }));
              }}
            >
              {intl.formatMessage({ id: 'goods.list.createGoodsButton' })}
            </AuthButton>
            <AuthButton
              type="primary"
              key="create"
              ghost={true}
              authority=""
              onClick={() => {
                setCreateGeneralGroupModalProps((preModalProps) => ({
                  ...preModalProps,
                  visible: true,
                  readOnly: false,
                  title: intl.formatMessage({ id: 'goods.list.createGeneralGroupTitle' }),
                }));
              }}
            >
              {intl.formatMessage({ id: 'goods.list.createGeneralGroupTitle' })}
            </AuthButton>
            <AuthButton
              ghost={true}
              type="primary"
              key="importGoods"
              authority="importGoods"
              onClick={() => {
                setImportModalProps((preModalProps) => ({ ...preModalProps, visible: true }));
              }}
            >
              {intl.formatMessage({ id: 'goods.list.importGoodsButton' })}
            </AuthButton>
            <AuthButton
              ghost={true}
              type="primary"
              authority="exportGoods"
              onClick={async () => {
                const params = formRef.current?.getFieldsValue();
                if (params.brandId) {
                  params.brandIdList = [params.brandId];
                }
                if (params.categoryId) {
                  params.categoryIdList = params.categoryId;
                }
                params.isFetchAllInventory = true;
                params.isFetchSupplier = true;
                params.isFetchWarehouseCostPrice = true;
                params.isFetchAllCostPrice = true;
                exportData({
                  systemId: 'GRIPX_STORE_SYS',
                  taskDesc: intl.formatMessage({ id: 'goods.list.exportGoodsTaskDesc' }),
                  moduleId: 'GOODS_MEMBER_EXPORT',
                  params: params,
                });
              }}
            >
              {intl.formatMessage({ id: 'goods.list.exportGoodsButton' })}
            </AuthButton>

            <AuthButton
              disabled={selectedRowKeys.length <= 0}
              danger
              authority="enableGoods"
              onClick={() => handleUpOrDownItem(selectedRowKeys, 1)}
            >
              {intl.formatMessage({ id: 'goods.list.onlineButton' })}
            </AuthButton>
            <AuthButton
              disabled={selectedRowKeys.length <= 0}
              danger
              authority="disableGoods"
              onClick={() => handleUpOrDownItem(selectedRowKeys, 0)}
            >
              {intl.formatMessage({ id: 'goods.list.offlineButton' })}
            </AuthButton>
          </Space>
        }
      />

      <GoodsCreateDrawerForm
        {...createModalProps}
        onCancel={hideCreateModal}
        onRefresh={() => {
          const timer = setTimeout(() => {
            actionRef.current?.reload(true);
            clearTimeout(timer);
          }, 1000);
        }}
      />
      <GeneralGroupCreateDrawer
        {...createGeneralGroupModalProps}
        onClose={() => {
          setCreateGeneralGroupModalProps({ visible: false });
        }}
        onSuccess={() => {
          setTimeout(() => {
            actionRef.current?.reload(true);
          }, 1000);
        }}
      />
      <GoodsImportModal {...importModalProps} onCancel={hideImportModal} />
      <GoodsDetailDrawer
        {...goodsDrawer}
        onClose={() => setGoodsDrawer({ visible: false })}
        refresh={() => {
          setTimeout(() => {
            actionRef.current?.reload(true);
          }, 1000);
        }}
      />
    </PageContainer>
  );
};

export default withKeepAlive(GoodsList);
