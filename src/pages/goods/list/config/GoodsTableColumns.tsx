import ColumnRender from '@/components/ColumnRender';
import type { StoreGoodsEntity } from '@/components/GoodsSearch/components/GoodsList/types/store.goods.entity';
import ImageList from '@/components/ImageList';
import AuthButton from '@/components/common/AuthButton';
import { queryPostList } from '@/pages/purchase/supplier/services';
import ColumnUtils from '@/utils/ColumnUtils';
import { transformCategoryTree } from '@/utils/transformCategoryTree';
import type { ProColumns } from '@ant-design/pro-components';
import { useIntl } from '@umijs/max';
import { Popconfirm, Space } from 'antd';
import { queryGoodsPropertyPage } from '../../property/services';
import { type GoodsEntity } from '../types/GoodsEntity.entity';
import { GoodsStatusValueEnum } from '../types/GoodsStatusValueEnum';
import { GoodsDetailDrawerProps, GoodsDetailType } from '@/components/GoodsDetailDrawer';

export interface PostListTableColumnsProps {
  handleUpOrDownItem: (ids: string[], status: number) => void;
  handleUpdateItem: (id: string) => void;
  setGoodsDrawer: (props: GoodsDetailDrawerProps) => void;
}
export const PostListTableColumns = (props: PostListTableColumnsProps) => {
  const intl = useIntl();
  return [
    {
      title: intl.formatMessage({ id: 'common.column.index' }),
      valueType: 'index',
      fixed: 'left',
      width: 40,
    },
    {
      title: intl.formatMessage({ id: 'goods.list.table.itemGroupName' }),
      dataIndex: 'itemGroupRoList',
      width: 120,
      formItemProps: {
        name: 'itemGroupName',
      },
      render: (_, record) => {
        return (
          <div>
            {record?.itemGroupRoList?.map((t) => (
              <div>
                <a
                  onClick={() =>
                    props.setGoodsDrawer({
                      visible: true,
                      groupId: t.id.toString(),
                      groupIds: record.itemGroupRoList?.map((item) => item.id.toString()),
                      type: GoodsDetailType.GeneralGroup,
                      itemId: record.itemId,
                      itemName: record.itemName,
                    })
                  }
                >
                  {t.name}
                </a>
              </div>
            ))}
          </div>
        );
      },
    },
    {
      title: intl.formatMessage({ id: 'goods.list.table.itemSn' }),
      dataIndex: 'itemSn',
      search: false,
      hideInSearch: true,
      ellipsis: true,
      width: 100,
      render: (_, record) => (
        <a
          onClick={() => {
            props.setGoodsDrawer({
              visible: true,
              itemId: record.itemId,
              itemName: record.itemName,
              groupIds: record.itemGroupRoList?.map((item) => item.id.toString()),
              type: GoodsDetailType.GoodInfo,
            });
          }}
        >
          {record.itemSn}
        </a>
      ),
    },
    {
      title: intl.formatMessage({ id: 'goods.list.table.itemName' }),
      dataIndex: 'images',
      search: false,
      width: 140,
      ellipsis: true,
      render: (_, record: StoreGoodsEntity) => {
        return (
          <ImageList
            itemName={<span className="text-ellipsis overflow-hidden">{record.itemName}</span>}
            urls={record?.images as string[]}
          />
        );
      },
    },

    {
      dataIndex: 'queryKeyWord',
      search: true,
      hideInTable: true,
      fieldProps: {
        placeholder: intl.formatMessage({ id: 'goods.list.table.queryKeywordPlaceholder' }),
      },
      formItemProps: {
        tooltip: intl.formatMessage({ id: 'goods.list.table.queryKeywordTooltip' }),
        label: intl.formatMessage({ id: 'goods.list.table.queryKeyword' }),
      },
    },

    {
      title: intl.formatMessage({ id: 'goods.list.table.oeNos' }),
      dataIndex: 'oeNos',
      search: false,
      width: 140,
      ellipsis: true,
      render: (_, entity) => ColumnRender.ArrayColumnRender(entity.oeNos),
    },
    {
      title: intl.formatMessage({ id: 'goods.list.table.brandPartNos' }),
      dataIndex: 'brandPartNos',
      search: false,
      width: 100,
      ellipsis: true,
      render: (_, entity) => ColumnRender.ArrayColumnRender(entity.brandPartNos),
    },
    {
      title: intl.formatMessage({ id: 'goods.list.table.brand' }),
      dataIndex: 'brandId',
      valueType: 'select',
      hideInTable: true,
      fieldProps: {
        showSearch: true,
        filterOption: false,
        optionRender: (option: any) => <Space>{option.data.label}</Space>,
      },
      request: async ({ keyWords: brandName }) => {
        const { data } = await queryGoodsPropertyPage(
          { brandName, pageNo: 1, pageSize: 1000 },
          'brand',
        );
        return data.map((t: any) => ({
          label: t.brandName,
          dataType: t.dataType,
          value: t.brandId,
        }));
      },
    },
    {
      title: intl.formatMessage({ id: 'goods.list.table.brandName' }),
      dataIndex: 'brandName',
      width: 100,
      ellipsis: true,
      hideInSearch: true,
    },
    {
      title: intl.formatMessage({ id: 'goods.list.table.category' }),
      dataIndex: 'categoryName',
      width: 100,
      ellipsis: true,
      hideInSearch: true,
    },
    {
      title: intl.formatMessage({ id: 'goods.list.table.tags' }),
      dataIndex: 'itemTagList',
      width: 100,
      formItemProps: {
        name: 'tagIdList',
      },
      render: (_, entity) =>
        ColumnRender.ArrayColumnRender(entity.itemTagList?.map((item) => item.tagName)),
      valueType: 'select',
      fieldProps: {
        showSearch: true,
        filterOption: false,
        mode: 'multiple',
      },
      request: async ({ keyWords }) => {
        const { data } = await queryGoodsPropertyPage(
          {
            tagName: keyWords,
            pageNo: 1,
            pageSize: 1000,
            tagStatus: 1,
          },
          'IcItemTagFacade',
        );
        return data?.map((t: any) => ({
          label: t.tagName,
          value: t.tagId,
        }));
      },
    },
    {
      title: intl.formatMessage({ id: 'goods.list.table.categoryName' }),
      dataIndex: 'categoryId',
      hideInTable: true,
      valueType: 'treeSelect',
      fieldProps: {
        treeCheckable: true,
        maxTagCount: 3,
        filterTreeNode: (text: string, treeNode: any) => treeNode.text?.includes(text),
      },
      request: () => {
        return queryGoodsPropertyPage(
          { pageSize: 999, pageNo: 1, isReturnTree: true },
          'category',
        ).then((result) => transformCategoryTree(result.data));
      },
    },
    {
      title: intl.formatMessage({ id: 'goods.list.table.unit' }),
      dataIndex: 'unitName',
      width: 50,
      search: false,
      ellipsis: true,
    },
    {
      title: intl.formatMessage({ id: 'goods.list.table.spec' }),
      dataIndex: 'spec',
      width: 60,
      search: false,
      ellipsis: true,
    },

    {
      title: intl.formatMessage({ id: 'goods.list.table.originRegion' }),
      dataIndex: 'originRegionName',
      width: 100,
      search: false,
      ellipsis: true,
    },
    {
      title: intl.formatMessage({ id: 'goods.list.table.supplier' }),
      dataIndex: 'supplierList',
      width: 150,
      ellipsis: true,
      valueType: 'select',
      fieldProps: {
        showSearch: true,
        filterOption: false,
      },
      formItemProps: {
        name: 'supplierId',
      },
      renderText: (_, record) => {
        // @ts-ignore
        return record?.supplierList?.map((t) => t.supplierName).join(',');
      },
      request: async ({ keyWords }) => {
        const { data } = await queryPostList({ supplierName: keyWords, pageNo: 1, pageSize: 1000 });
        // @ts-ignore
        return data?.map((t) => ({
          label: t.supplierInfo?.supplierName,
          value: t.supplierInfo?.id,
        }));
      },
    },
    {
      title: intl.formatMessage({ id: 'goods.list.table.suggestPrice' }),
      dataIndex: 'suggestPrice',
      width: 80,
      search: false,
      valueType: 'money',
    },
    {
      title: '成本价',
      dataIndex: 'allCostPrice',
      width: 80,
      search: false,
      valueType: 'money',
    },
    {
      title: intl.formatMessage({ id: 'goods.list.table.lowPrice' }),
      dataIndex: 'lowPrice',
      width: 80,
      search: false,
      valueType: 'money',
    },
    {
      title: intl.formatMessage({ id: 'goods.list.table.inventoryNum' }),
      dataIndex: 'totalInventoryNum',
      width: 60,
      search: false,
      render: (_, record: GoodsEntity) => {
        const sumCount = record?.totalInventoryNum ?? 0;
        return (
          <span
            className={`text-[#1677ff] cursor-pointer`}
            onClick={(e) => {
              e.stopPropagation();
              props.setGoodsDrawer({
                visible: true,
                groupIds: record.itemGroupRoList?.map((item) => item.id.toString()),
                type: GoodsDetailType.LocalStock,
                itemId: record.itemId,
                itemName: record.itemName,
              });
            }}
          >
            {sumCount}
          </span>
        );
      },
    },
    {
      title: intl.formatMessage({ id: 'goods.list.table.adaptModel' }),
      dataIndex: 'adaptModel',
      width: 120,
      search: false,
      ellipsis: true,
    },
    {
      title: intl.formatMessage({ id: 'goods.list.table.remark' }),
      dataIndex: 'remark',
      width: 120,
      search: false,
      ellipsis: true,
    },
    {
      title: intl.formatMessage({ id: 'goods.list.table.memCode' }),
      dataIndex: 'memCode',
      width: 120,
      search: false,
      ellipsis: true,
    },
    // TODO 版本2
    // {
    //   title: '创建时间',
    //   width: 160,
    //   dataIndex: 'createTime',
    //   hideInSearch: true,
    // },
    {
      title: intl.formatMessage({ id: 'goods.list.table.status' }),
      dataIndex: 'itemStatus',
      valueType: 'select',
      search: true,
      width: 80,
      valueEnum: GoodsStatusValueEnum,
    },
    {
      title: intl.formatMessage({ id: 'common.column.operation' }),
      search: false,
      width: 100,
      valueType: 'option',
      fixed: 'right',
      render: (_, record: GoodsEntity) => {
        const { itemId, itemStatus } = record;
        const status = itemStatus ^ 1;
        const operType: string = ColumnUtils.getText(GoodsStatusValueEnum, status);
        return (
          <Space>
            <Popconfirm
              title={intl.formatMessage({ id: 'goods.list.table.confirmOperation' }, { operType })}
              onConfirm={() => {
                props.handleUpOrDownItem([itemId], status);
              }}
            >
              <AuthButton isHref authority="enableOrDisableGoods">
                {status == 1
                  ? intl.formatMessage({ id: 'goods.list.table.operation.enable' })
                  : intl.formatMessage({ id: 'goods.list.table.operation.disable' })}
              </AuthButton>
            </Popconfirm>
            <AuthButton isHref authority="editGoods" onClick={() => props.handleUpdateItem(itemId)}>
              {intl.formatMessage({ id: 'common.button.edit' })}
            </AuthButton>
          </Space>
        );
      },
    },
  ] as ProColumns<GoodsEntity>[];
};
