import { FormattedMessage } from "@umijs/max";

export enum CstType {
  REPAIR_SHOP = 1,
  REPAIR_MAN,
  RETAIL,
}

export const cstTypeOptions = [
  { label: <FormattedMessage id="customer.customerList.type.repairShop" />, value: CstType.REPAIR_SHOP },
  { label: <FormattedMessage id="customer.customerList.type.repairMan" />, value: CstType.REPAIR_MAN },
  { label: <FormattedMessage id="customer.customerList.type.seller" />, value: CstType.RETAIL },
];
