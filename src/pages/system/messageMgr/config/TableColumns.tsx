import ColumnRender from '@/components/ColumnRender';
import AuthButton from '@/components/common/AuthButton';
import type { ProColumns } from '@ant-design/pro-components';
import { Popconfirm, Space } from 'antd';
import { MessageEntity } from '../types/MessageEntity';
import { MessageStatusValueEnum } from '../types/MessageStatusType';
import { NoticeTypeValueEnum } from '../types/noticeType';

export interface PostListTableColumnsProps {
    handleDetailItem: (id?: number) => void;
    handleUpdateItem: (id?: number) => Promise<boolean>;
    intl: any;
}

export const PostListTableColumns = (props: PostListTableColumnsProps) => {
    const t = (id: string, ...rest) => props.intl.formatMessage({ id }, ...rest);
    return [
        {
            title: t('common.column.index'),
            valueType: 'index',
            fixed: 'left',
            width: 50,
        },
        {
            title: t('system.messageMgr.column.title'),
            dataIndex: 'title',
            fixed: 'left',
            width: 140,
            ellipsis: true,
        },
        {
            title: t('system.messageMgr.column.content'),
            dataIndex: 'content',
            search: false,
            ellipsis: true,
            width: 150,
            renderText: ColumnRender.RichContentColumnRender
        },
        {
            title: t('system.messageMgr.column.status'),
            dataIndex: 'isDelete',
            search: true,
            width: 80,
            valueEnum: MessageStatusValueEnum(props.intl),
        },
        {
            title: t('system.messageMgr.column.noticeType'),
            dataIndex: 'noticeType',
            search: false,
            width: 100,
            valueEnum: NoticeTypeValueEnum(props.intl),
        },
        {
            title: t('system.messageMgr.column.createTime'),
            width: 170,
            dataIndex: 'createTime',
            hideInSearch: true,
        },
        {
            title: t('system.messageMgr.column.createTime'),
            dataIndex: 'createTime',
            valueType: "dateTimeRange",
            hideInTable: true,
            search: {
                transform: ([createTimeStart, createTimeEnd]) => ({ createTimeStart, createTimeEnd }),
            },
        },
        {
            title: t('system.messageMgr.column.sendTime'),
            dataIndex: 'sendTime',
            search: false,
            width: 150,
        },
        {
            title: t('system.messageMgr.column.overdueTime'),
            dataIndex: 'overdueTime',
            search: false,
            width: 150,
            valueType: 'dateTime',
        },
        {
            title: t('system.messageMgr.column.updatePerson'),
            dataIndex: 'updatePerson',
            width: 100,
            search: false,
        },
        {
            title: t('common.column.operation'),
            key: 'operation',
            search: false,
            fixed: 'right',
            width: 200,
            render: (text, record: MessageEntity) => (
                <Space>
                    <AuthButton
                        isHref
                        // authority="SYSTEM_STORE_UPDATE"
                        // authority="SYSTEM_MESSAGE_DETAIL"
                        onClick={() => { props.handleDetailItem(record.id) }}
                    >
                        {t('common.button.view')}
                    </AuthButton>
                    <Popconfirm
                        title={t('common.tip.confirm.action', { action: t('common.button.void').toLowerCase() })}
                        onConfirm={() => {
                            props.handleUpdateItem(record.id)
                        }}
                    >
                        <AuthButton isHref
                            visible={record.isDelete == 0}
                        // authority="SYSTEM_STORE_DELETE"
                        // authority="SYSTEM_MESSAGE_DISABLE"
                        >
                            {t('common.button.void')}
                        </AuthButton>
                    </Popconfirm>
                </Space >
            ),
        },
    ] as ProColumns<MessageEntity>[];
}
