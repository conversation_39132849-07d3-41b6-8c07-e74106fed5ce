import { setSessionCookie } from '@/access';
import Footer from '@/components/Footer';
import { getInitSystemInfo } from '@/services/systerm';
import { md5 } from '@/utils/md5';
import { LockOutlined, MailOutlined, TabletOutlined } from '@ant-design/icons';
import { LoginForm, ProForm, ProFormCaptcha, ProFormText } from '@ant-design/pro-components';
import { useIntl, useModel, useSearchParams } from '@umijs/max';
import { useAsyncEffect, useBoolean } from 'ahooks';
import { Button, ConfigProvider, Flex, message, Spin } from 'antd';
import { useForm } from 'antd/es/form/Form';
import React, { useState } from 'react';
import { flushSync } from 'react-dom';
import SetPassword from './components/setPassword';
import { loginPost, modifyPasswordPost, sendAuthcodePost } from './services';
import type { QueryPostListRequest } from './types/query.post.list.request';

const Login: React.FC = () => {
  const intl = useIntl();
  const [userLoginState, setUserLoginState] = useState({});
  const { initialState, setInitialState } = useModel('@@initialState');
  const [flag, { toggle: setFlag, setTrue, setFalse }] = useBoolean(true); //flag :true 登录 false:设置密码
  const [form] = useForm();
  const [passTitle, setPassTitle] = useState(intl.formatMessage({ id: 'login.setNewPassword' }));
  const [searchParams, setSearchParams] = useSearchParams();
  const [loading, setLoading] = useState(false);

  useAsyncEffect(async () => {
    if (searchParams?.get('first')) {
      if (searchParams?.get('first') == 'YES') {
        setFalse();
        setPassTitle(intl.formatMessage({ id: 'login.setInitialPassword' }));
      }
    }
  }, [searchParams]);

  const fetchUserInfo = async () => {
    const {
      currentUser,
      currentUserInfo,
      systemMenu: systemMenu,
      buttonItem,
    } = await getInitSystemInfo();
    if (currentUser?.accountId) {
      flushSync(() => {
        setInitialState((s) => ({
          ...s,
          currentUser,
          currentUserInfo,
          systemMenu,
          buttonItem,
        }));
      });
    }
  };

  const handleSubmit = async (values: QueryPostListRequest) => {
    try {
      // 登录 手机号登录phone  用户名name
      const result = await loginPost({ ...values, type: 0 });
      if (result && result.code == 0 && result.data) {
        const {
          data: { sSessionId, accountId },
        } = result;
        if (sSessionId) {
          setSessionCookie(sSessionId);
        }
        await fetchUserInfo();
        const urlParams = new URL(window.location.href).searchParams;
        // history.push(urlParams.get('redirect') || '/');
        window.location.href = urlParams.get('redirect') || '/';
        return;
      } else if (result.code == 100001) {
        //首次登录修改密码
        setFalse();
        setPassTitle(intl.formatMessage({ id: 'login.setInitialPassword' }));
        form.setFieldValue('email', values.email);
      }
      // 如果失败去设置用户错误信息
      setUserLoginState(result);
    } catch (error) {
      console.log(error);

      message.error(intl.formatMessage({ id: 'login.loginFailed' }));
    }
  };
  /**
   * 重置密码
   * @param values
   */
  const handleModify = async (values: QueryPostListRequest) => {
    setLoading(true);
    modifyPasswordPost(values)
      .then((res) => {
        if (res.code == 0 && res.data) {
          //修改成功
          message.success(intl.formatMessage({ id: 'login.resetSuccess' }));
          setLoading(true);
          handleSubmit({ ...values, password: values.newPassword }).finally(() => {
            setLoading(false);
          });
        } else {
          message.error(res.msg);
        }
      })
      .finally(() => {
        setLoading(false);
      });
  };

  const onCancel = async () => {
    setTrue();
    form.resetFields(); //重置表单
  };

  return (
    <ConfigProvider
      theme={{
        token: {
          colorPrimary: '#F49C1F',
        },
      }}
    >
      <Flex
        className="h-screen bg-contain"
        vertical
        justify="center"
        style={{ backgroundImage: 'url(/bg.png)' }}
      >
        <Flex
          justify="center"
          align="center"
          style={{
            backgroundImage: 'url(/left.png) ',
            backgroundSize: '280px 100%',
            paddingLeft: 280,
            margin: '0 auto',
          }}
          className="bg-no-repeat"
        >
          <div className="overflow-hidden rounded-r-lg bg-white w-[450px]">
            {flag ? (
              <LoginForm
                title={
                  <span className="text-[20px]">{intl.formatMessage({ id: 'login.title' })}</span>
                }
                initialValues={{
                  autoLogin: true,
                }}
                onFinish={async (values) => {
                  await handleSubmit(values);
                }}
                actions={
                  <Flex vertical>
                    <div>
                      <span
                        className="cursor-pointer float-right"
                        onClick={() => {
                          setFlag();
                          setPassTitle(intl.formatMessage({ id: 'login.setNewPassword' }));
                        }}
                      >
                        {intl.formatMessage({ id: 'login.forgotPassword' })}
                      </span>
                    </div>
                  </Flex>
                }
              >
                <div className="mt-10">
                  <ProFormText
                    name="email"
                    fieldProps={{
                      size: 'large',
                      prefix: <MailOutlined />,
                    }}
                    placeholder={intl.formatMessage({ id: 'login.email' })}
                    rules={[
                      {
                        required: true,
                        message: intl.formatMessage({ id: 'login.emailRequired' }),
                      },
                    ]}
                  />
                  <ProFormText.Password
                    name="password"
                    fieldProps={{
                      size: 'large',
                      prefix: <LockOutlined />,
                    }}
                    placeholder={intl.formatMessage({ id: 'login.password' })}
                    rules={[
                      {
                        required: true,
                        message: intl.formatMessage({ id: 'login.passwordRequired' }),
                      },
                    ]}
                    transform={(value) => md5(value)}
                  />
                </div>
              </LoginForm>
            ) : (
              <Spin spinning={loading}>
                <ProForm
                  key="setProForm"
                  submitter={{
                    render: (props, doms) => {
                      return [
                        <div key="buttons" className="flex justify-between">
                          <Button
                            type="primary"
                            className="w-[150px] h-[40px]"
                            key="ok"
                            onClick={() => props.form?.submit?.()}
                          >
                            {intl.formatMessage({ id: 'common.button.confirm' })}
                          </Button>
                          <Button
                            key="cancel"
                            className="w-[150px] h-[40px]"
                            onClick={() => onCancel()}
                          >
                            {intl.formatMessage({ id: 'common.button.cancel' })}
                          </Button>
                        </div>,
                      ];
                    },
                  }}
                  onFinish={async (values) => {
                    await handleModify(values);
                  }}
                  form={form}
                  className="px-[50px] pb-[40px]"
                >
                  <SetPassword title={passTitle} />
                </ProForm>
              </Spin>
            )}
          </div>
        </Flex>
        <Footer />
      </Flex>
    </ConfigProvider>
  );
};

export default Login;
