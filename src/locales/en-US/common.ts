export default {
  'column.index': 'No.',
  'column.operation': 'Operation',
  'column.createTime': 'Create Time',
  'column.updateTime': 'Update Time',
  'column.operationTime': 'Operation Time',
  'column.status': 'Status',
  'column.remark': 'Remark',
  'column.contact': 'Contact',
  'column.contactPhone': 'Contact Phone',
  'column.contactAddress': 'Contact Address',
  'unit.yuan': 'Yuan',
  'unit.day': 'Days',
  'option.yes': 'Yes',
  'option.no': 'No',
  'option.enable': 'Enable',
  'option.disable': 'Disable',
  'option.active': 'Active',
  'option.inactive': 'Inactive',
  'option.peddingAudit': 'Pending Audit',
  'button.add': 'Add',
  'button.save': 'Save',
  'button.confirm': 'Confirm',
  'button.audit': 'Aduit',
  'button.receive': 'Receive',
  'button.edit': 'Edit',
  'button.cancel': 'Cancel',
  'button.close': 'Close',
  'button.export': 'Export',
  'button.download': 'Download',
  'button.batchImport': 'Batch Import',
  'button.void': 'Void',
  'button.downloadTemplate': 'Download Template',
  'button.delete': 'Delete',
  'button.enable': 'Enable',
  'button.disable': 'Disable',
  'button.preview': 'Preview',
  'button.print': 'Print',
  'button.copy': 'Copy',
  'button.view': 'View',
  'button.back': 'Back',
  'button.withdraw': 'Withdraw',
  'placeholder.input': 'Please enter',
  'placeholder.select': 'Please select',

  'placeholder.inputWithMaxLength': 'Please enter, no more than {maxLength} characters',
  'label.remark': 'Remark',
  'confirm.delete': 'Are you sure to delete?',
  'noData': 'No Data',

  'field.brand': 'Brand',
  'field.category': 'Category',
  'field.currency': 'Currency',
  'field.rate': 'Rate',

  'message.needSelectOne': 'Please select at least one item.',
  'message.deleteSuccess': 'Deleted successfully',
  'message.addSuccess': 'Added successfully',
  'message.submitSuccess': 'Submitted successfully',
  'message.operationSuccess': 'Operation Success',
  'message.editableTable.inputError':
    'Please check the form data, there are required fields that have not been filled in or the data format is incorrect',
  'message.save.success': 'Saved successfully',
  'message.operation.success': 'Operation Success',

  // Discount types
  'discount.type.none': 'No Discount',
  'discount.type.wholeOrderDiscount': 'Whole Order Discount',
  'discount.type.wholeOrderReduction': 'Whole Order Reduction',

  // Payment methods
  'payment.method.account': 'Account',
  'payment.method.cash': 'Cash',

  // Delivery methods
  'delivery.method.express': 'Express',

  // Search types
  'search.type.exact': 'Exact Search',
  'search.type.inStockOnly': 'In Stock Only',

  // Overdue status
  'overdue.status.normal': 'Not Overdue',
  'overdue.status.overdue': 'Overdue',
  'overdue.status.disabled': 'Disabled',

  // Credit log types
  'credit.log.type.addAccount': 'Add Credit Account',
  'credit.log.type.modifyAccount': 'Modify Credit Account',
  'credit.log.type.enable': 'Enable',
  'credit.log.type.lock': 'Lock',
  'credit.log.type.cancel': 'Cancel',

  // Finance tag source
  'finance.tag.source.system': 'System Preset',
  'finance.tag.source.custom': 'Custom',

  // Finance ledger types
  'finance.ledger.type.income': 'Income',
  'finance.ledger.type.expense': 'Expense',

  // StocksInfoDrawer component
  'stocksInfoDrawer.title': 'Local Stock',
  'stocksInfoDrawer.totalInventory': 'Total Inventory',
  'stocksInfoDrawer.occupiedInventory': 'Occupied Inventory',
  'stocksInfoDrawer.availableInventory': 'Available Inventory',
  'stocksInfoDrawer.stockDistribution': 'Stock Distribution',
  'stocksInfoDrawer.warehouseName': 'Warehouse Name',
  'stocksInfoDrawer.totalStock': 'Total Stock',
  'stocksInfoDrawer.occupiedStock': 'Occupied Stock',
  'stocksInfoDrawer.availableStock': 'Available Stock',
  'stocksInfoDrawer.purchaseInTransit': 'Purchase in Transit',
  'stocksInfoDrawer.upperLimit': 'Upper Limit',
  'stocksInfoDrawer.lowerLimit': 'Lower Limit',
  'stocksInfoDrawer.selected': 'Selected',

  // PriceInfoDrawer component
  'priceInfoDrawer.priceInfo': 'Price Information',
  'priceInfoDrawer.purchaseHistory': 'Purchase History',
  'priceInfoDrawer.goodsPrice': 'Goods Price',
  'priceInfoDrawer.salePrice': 'Sale Price',
  'priceInfoDrawer.lowestPrice': 'Lowest Price',
  'priceInfoDrawer.costPrice': 'Cost Price',
  'priceInfoDrawer.customerLastFivePrices': 'Customer Last 5 Sale Prices',
  'priceInfoDrawer.storeLastFivePrices': 'Store Last 5 Sale Prices',
  'priceInfoDrawer.lastTenPurchases': 'Last 10 Purchase History',
  'priceInfoDrawer.orderNo': 'Order No.',
  'priceInfoDrawer.price': 'Price',
  'priceInfoDrawer.quantity': 'Quantity',
  'priceInfoDrawer.time': 'Time',
  'priceInfoDrawer.customerName': 'Customer Name',
  'priceInfoDrawer.purchaseOrderNo': 'Purchase Order No.',
  'priceInfoDrawer.supplier': 'Supplier',
  'priceInfoDrawer.purchaseQuantity': 'Purchase Quantity',
  'priceInfoDrawer.purchasePrice': 'Purchase Price',
  'priceInfoDrawer.purchaseTime': 'Purchase Time',

  // Export utility
  'export.title': 'Notice',
  'export.content':
    'Export task has been created successfully. Do you want to view the export results?',
  'export.okText': 'View Results',

  // Import utility
  'import.title': 'Batch Import',
  'import.description': 'Please import data according to the provided template',
  'import.downloadTemplate': 'Download Template',
  'import.importFile': 'Import File',
  'import.processingTitle': 'Notice',
  'import.processingContent': 'Importing, please wait...',
  'import.successTitle': 'Notice',
  'import.successContent':
    'Import task has been created successfully. Do you want to view the import results?',
  'import.successOkText': 'View Results',
  'import.successMessage': 'Import successful',

  'confirm.title': 'Confirm',
  'tip.confirm.action': 'Are you sure to {action}?',
};
