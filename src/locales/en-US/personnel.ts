export default {
  'personnel.performance.performanceTime': 'Performance Time',
  'personnel.performance.employee': 'Employee',
  'personnel.performance.performanceItem': 'Performance Item',
  'personnel.performance.performanceValue': 'Performance Value',
  'personnel.performance.performanceDesc': 'Performance Desc',
  'personnel.performance.reward': 'Performance Reward',
  'personnel.performance.updatedBy': 'Updated By',
  'personnel.performance.updatedAt': 'Updated At',
  'personnel.performance.status': 'Status',
  'personnel.performance.button.import': 'Import Performance',
  'personnel.performance.button.export': 'Export Performance',
  'personnel.performance.button.cancel': 'Discard',
  'personnel.leave.applyLeave': 'Apply Leave',
  'personnel.leave.leaveType': 'Leave Type',
  'personnel.leave.endTime': 'End Time',
  'personnel.leave.hours': 'Leave Hours',
  'personnel.leave.reason': 'Leave Reason',
  'personnel.leave.payLeave': 'Pay Leave',
  'personnel.leave.paidAmount': 'Paid Amount',
  'personnel.leave.adjustLeaveType': 'Adjust Type',
  'personnel.leave.approveLeave': 'Approve Leave',
  'personnel.leave.approveState': 'Approve State',
  'personnel.leave.employee': 'Employee',
  'personnel.leave.applyPerson': 'Apply Person',
  'personnel.leave.state': 'State',
  'personnel.leave.approve': 'Approve',
  'personnel.leave.images': 'Images',
  'personnel.leave.comments': 'Comments',
  'personnel.leave.remainingLeave': 'Remaining Leave',
  'personnel.leave.payLeaveHours': 'Adjust Leave Hours',
  'personnel.leave.hoursUnit': 'hours',
  'personnel.leave.leaveInfo': 'Leave Info',
  'personnel.leave.applicant': 'Applicant',
  'personnel.leave.submitter': 'Submitter',
  'personnel.leave.remainingHours': 'Remaining Hours',
  'personnel.leave.leaveTime': 'Leave Time',
  'personnel.leave.leaveDuration': 'Leave Duration',
  'personnel.leave.doctorCertificate': 'Doctor Certificate',
  'personnel.leave.approvalResult': 'Approval Result',
  'personnel.leave.message.leaveHoursRequired': 'Leave hours cannot be 0',
  'personnel.leave.message.leaveCancelConfirm': 'Are you sure you want to cancel this leave application?',
  'personnel.leave.message.leaveVoidConfirm': 'Are you sure you want to void this leave application?',

  // Page titles and labels
  'personnel.leave.tab.leaveRecord': 'Leave Records',
  'personnel.leave.tab.leaveBalance': 'Leave Balance',

  // Leave types
  'personnel.leave.type.annualLeave': 'Annual Leave',
  'personnel.leave.type.sickLeave': 'Sick Leave',
  'personnel.leave.type.longServiceLeave': 'Long Service Leave',
  'personnel.leave.type.unpaidLeave': 'Unpaid Leave',
  'personnel.leave.type.otherLeave': 'Other Leave',

  // Leave status
  'personnel.leave.status.pendingReview': 'Pending Review',
  'personnel.leave.status.approved': 'Approved',
  'personnel.leave.status.rejected': 'Rejected',
  'personnel.leave.status.cancelled': 'Cancelled',

  // Exchange channel types
  'personnel.leave.exchangeChannel.leave': 'Leave',
  'personnel.leave.exchangeChannel.increaseLeave': 'Increase Leave',
  'personnel.leave.exchangeChannel.decreaseLeave': 'Decrease Leave',

  // Table column headers
  'personnel.leave.column.store': 'Store',
  'personnel.leave.column.recordType': 'Record Type',
  'personnel.leave.column.submitter': 'Submitter',
  'personnel.leave.column.approver': 'Approver',
  'personnel.leave.column.approveTime': 'Approve Time',
  'personnel.leave.column.approvalResult': 'Approval Result',
  'personnel.leave.help.leaveRemainHours': 'Leave Hours Balance',
  'personnel.leave.help.leaveHours': 'Leave Hours',

  // Leave balance related
  'personnel.leave.balance.total': 'Total',
  'personnel.leave.balance.used': 'Used',
  'personnel.leave.balance.adjust': 'Adjust',
  'personnel.leave.balance.remaining': 'Remaining',
  'personnel.leave.balance.unitConversion': 'Toggle to view leave in days/weeks.The conversion is based on your most recent weekly working hours.',
  'personnel.leave.balance.serviceLeaveLessThan7Years': 'Not full 7 years',
  'personnel.leave.balance.detail': 'Leave Detail',
  'personnel.leave.balance.year': 'Year',
  'personnel.leave.balance.currentTotalHours': 'Remaining Year Leave',

  // Action buttons
  'personnel.leave.action.withdraw': 'Withdraw',
  'personnel.leave.action.void': 'Void',

  // Units
  'personnel.leave.unit.hour': 'hour',
  'personnel.leave.unit.day': 'day',
  'personnel.leave.unit.week': 'week',
};
