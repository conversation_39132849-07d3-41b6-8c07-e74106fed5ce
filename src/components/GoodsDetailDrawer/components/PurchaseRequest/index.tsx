import {
  ProDescriptions,
  ProForm,
  ProFormDigit,
  ProFormInstance,
  ProFormSelect,
  ProFormTextArea,
} from '@ant-design/pro-components';
import { Button, Empty, message } from 'antd';
import LeftTitle from '@/components/LeftTitle';
import { querySupplierList } from '@/pages/purchase/supplier/services';
import { useIntl } from '@@/exports';
import { REQUIRED_RULES } from '@/utils/RuleUtils';
import { queryStoreByAccount } from '@/pages/personnel/user/services';
import { warehouseList } from '@/pages/stocks/warehouse/services';
import { YesNoStatus } from '@/pages/purchase/supplier/operation/types/YesNo';
import { MAX_AMOUNT } from '@/utils/Constants';
import { ProFormMoney } from '@ant-design/pro-form';
import { useEffect, useRef, useState } from 'react';
import { addPurchaseRequestDetail } from '@/components/GoodsDetailDrawer/services';
import { queryPurchaseRequestList } from '@/pages/purchase/request/list/services';
import { PurchaseRequestEntity } from '@/pages/purchase/request/types/purchaseRequest.entity';
import { GoodsEntity } from '@/pages/goods/list/types/GoodsEntity.entity';
import { goodsDetail } from '@/pages/goods/list/services';
import BraftEditor from 'braft-editor';

export interface PurchaseRequestProps {
  warehouseId?: string;
  storeId?: string;
  saleOrderNo?: string;
  itemId?: string;
}

export default function PurchaseRequest(props: PurchaseRequestProps) {
  const intl = useIntl();
  const formRef = useRef<ProFormInstance>();
  const { warehouseId, storeId, saleOrderNo, itemId } = props;
  const [loading, setLoading] = useState(false);
  const [list, setList] = useState<PurchaseRequestEntity[]>();
  const [detail, setDetail] = useState<GoodsEntity>();

  useEffect(() => {
    if (warehouseId && storeId) {
      formRef.current?.setFieldsValue({
        warehouseId,
        storeId,
      });
    }
    return () => {
      formRef.current?.resetFields();
    };
  }, [warehouseId, storeId]);

  useEffect(() => {
    if (itemId) {
      queryList();
      goodsDetail({ itemId }).then((res) => {
        // @ts-ignore
        setDetail(res);
      });
    }
    return () => {
      setList(undefined);
      setDetail(undefined);
    };
  }, [itemId]);

  const queryList = () => {
    queryPurchaseRequestList({ pageSize: 99999, itemId, sourceNo: saleOrderNo }).then((res) =>
      setList(res.data),
    );
  };

  const handleSubmit = (values: any) => {
    setLoading(true);
    addPurchaseRequestDetail({ ...values, sourceNo: saleOrderNo, itemId })
      .then((res) => {
        if (res) {
          message.success('提交成功');
          queryList();
          formRef.current?.resetFields(['supplierId', 'price', 'num', 'remark']);
        }
      })
      .finally(() => {
        setLoading(false);
      });
  };

  if (!saleOrderNo) {
    return (
      <Empty
        description={<span className="text-gray-500">请先添加商品创建销售单，再进行采购申请</span>}
      />
    );
  }

  return (
    <div>
      <ProForm
        formRef={formRef}
        layout="vertical"
        grid
        colProps={{ span: 8 }}
        submitter={false}
        onFinish={handleSubmit}
      >
        <ProFormSelect
          name="supplierId"
          label={intl.formatMessage({ id: 'purchase.external.label.supplier' })}
          placeholder={intl.formatMessage({
            id: 'purchase.external.placeholder.selectSupplier',
          })}
          rules={[REQUIRED_RULES]}
          fieldProps={{ fieldNames: { label: 'supplierName', value: 'supplierId' } }}
          request={querySupplierList}
          onChange={(value) => {
            const defaultPrice = detail?.supplierList?.find(
              (item) => item.supplierId === value,
            )?.purchasePrice;
            formRef.current?.setFieldsValue({ price: defaultPrice ?? '' });
          }}
        />
        <ProFormSelect
          label={'采购门店'}
          name="storeId"
          rules={[REQUIRED_RULES]}
          allowClear={false}
          fieldProps={{ fieldNames: { label: 'name', value: 'id' } }}
          request={() => queryStoreByAccount({ status: 1 })}
        />
        <ProFormSelect
          label={'收货仓库'}
          name="warehouseId"
          disabled={true}
          rules={[REQUIRED_RULES]}
          fieldProps={{ fieldNames: { label: 'warehouseName', value: 'warehouseId' } }}
          request={() =>
            warehouseList({
              state: YesNoStatus.YES,
              storeIdList: [props.storeId!],
            }).then((res) => res.warehouseStoreRelationRoList ?? [])
          }
        />
        <ProFormMoney
          label="采购价"
          name="price"
          rules={[REQUIRED_RULES]}
          fieldProps={{
            precision: 2,
            min: 0,
            max: MAX_AMOUNT,
          }}
        />
        <ProFormDigit
          label="采购数量"
          name="num"
          rules={[REQUIRED_RULES]}
          fieldProps={{
            precision: 0,
            min: 0,
            max: MAX_AMOUNT,
          }}
        />
        <ProFormTextArea
          label="备注"
          name="remark"
          colProps={{ span: 24 }}
          fieldProps={{ maxLength: 100, count: { max: 100, show: true } }}
        />
      </ProForm>
      <div className="flex justify-end mt-6">
        <Button type="primary" loading={loading} onClick={() => formRef.current?.submit()}>
          确认采购
        </Button>
      </div>
      {list && list?.length > 0 && (
        <div className="mt-10">
          <LeftTitle title="申请记录" />
          <div className="flex flex-col gap-5 mt-3">
            {list?.map((item) => (
              <div className="border border-solid border-gray-200 p-3">
                <div className="font-medium mb-3">2025-08-24 10:00:00</div>
                <ProDescriptions
                  columns={[
                    { label: '供应商', dataIndex: 'supplierName' },
                    { label: '采购门店', dataIndex: 'storeName' },
                    { label: '收货仓库', dataIndex: 'warehouseName' },
                    { label: '采购价', dataIndex: 'price' },
                    { label: '采购数量', dataIndex: 'num' },
                    { label: '备注', dataIndex: 'remark' },
                  ]}
                  dataSource={item}
                />
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}
