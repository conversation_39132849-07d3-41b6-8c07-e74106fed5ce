import { InfoCircleOutlined } from '@ant-design/icons';
import {
  ProForm,
  ProFormDigit,
  ProFormInstance,
  ProFormSelect,
  ProFormTextArea,
} from '@ant-design/pro-components';
import { REQUIRED_RULES } from '@/utils/RuleUtils';
import { Button, Empty, message } from 'antd';
import { useState } from 'react';
import { submitTransferPost, transferCreateOrUpdatePost } from '@/pages/stocks/transfer/services';
import { TransferBillType } from '@/pages/stocks/transfer/list/types/TransferStatusEnum';
import { QueryPurchaseRecordResponse } from '@/components/GoodsDetailDrawer/components/StocksInfo/types/query.purchase.record.response';

export interface TransferRequestProps {
  formWarehouse?: any;
  toWarehouse?: any;
  detail?: QueryPurchaseRecordResponse;
  storeId?: string;
  saleOrderNo?: string;
  itemIdList?: string[];
  transferFrom?: ProFormInstance;
}

export default function TransferRequest(props: TransferRequestProps) {
  const { formWarehouse, toWarehouse, detail, storeId, saleOrderNo, itemIdList, transferFrom } =
    props;
  const [loading, setLoading] = useState(false);

  const handleTransfer = (values: any) => {
    setLoading(true);
    const { transferNum, ...rest } = values;
    const storeIdOut = detail?.stockInventoryRos?.find(
      (item) => item.warehouseId === values.warehouseIdOut,
    )?.storeIdList?.[0];
    transferCreateOrUpdatePost({
      ...rest,
      storeIdOut,
      storeIdIn: storeId,
      billType: TransferBillType.SALE_TRANSFER,
      origBillNo: saleOrderNo,
      stockTransferDetailCmdList: itemIdList?.map((item) => ({ itemId: item, transferNum })),
    })
      .then((data) => {
        if (data) {
          setTimeout(() => {
            setLoading(true);
          }, 0);
          submitTransferPost({ id: data.id, bizBillNo: data.bizBillNo })
            .then((res) => {
              if (res) {
                message.success(`提交调拨成功`);
                transferFrom?.resetFields(['remarks', 'transferNum']);
              }
            })
            .finally(() => {
              setLoading(false);
            });
        }
      })
      .finally(() => {
        setLoading(false);
      });
  };

  if (!saleOrderNo) {
    return (
      <Empty
        description={<span className="text-gray-500">请先添加商品创建销售单，再进行调拨申请</span>}
      />
    );
  }

  return (
    <div>
      <div className="text-gray-500 flex gap-1 mb-3">
        <InfoCircleOutlined />
        点击库存分布调拨按钮，选择调出仓库
      </div>
      <ProForm
        grid={true}
        colProps={{ span: 8 }}
        submitter={false}
        form={transferFrom}
        onFinish={handleTransfer}
      >
        <ProFormSelect
          label="From"
          name="warehouseIdOut"
          rules={[REQUIRED_RULES]}
          options={formWarehouse}
          disabled={true}
        />
        <ProFormSelect
          label="TO"
          name="warehouseIdIn"
          rules={[REQUIRED_RULES]}
          options={toWarehouse}
          disabled={true}
        />
        <ProFormDigit
          fieldProps={{ precision: 0, min: 1 }}
          name="transferNum"
          rules={[REQUIRED_RULES]}
          label="调拨数量"
        />
        <ProFormTextArea
          label="备注"
          name="remarks"
          colProps={{ span: 24 }}
          fieldProps={{ maxLength: 100, count: { max: 100, show: true } }}
        />
        <div className="flex w-full justify-end mt-5">
          <Button type={'primary'} loading={loading} htmlType={'submit'}>
            确认调拨
          </Button>
        </div>
      </ProForm>
    </div>
  );
}
