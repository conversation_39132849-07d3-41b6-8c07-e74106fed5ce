import { paymentProcess } from '@/pages/sales/order/list/services';
import { CreatePaymentResponse } from '@/pages/sales/order/list/types/create.payment.response';
import { message, Spin } from 'antd';
import { useEffect, useState } from 'react';

interface CardPaymentFormProps {
  payData: CreatePaymentResponse;
  onCancel?: () => void;
  onSuccess?: () => void;
  bizNo?: string;
  saleOrgId?: string;
}

export default function CardPaymentForm({
  payData,
  onCancel,
  onSuccess,
  bizNo,
  saleOrgId,
}: CardPaymentFormProps) {
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (payData) {
      // @ts-ignore
      BPOINT.txn.authkey.setupPaymentMethodForm(payData.authKey, {
        appendToElementId: 'cardForm',
        card: {
          number: {
            label: 'Card number',
          },
          expiry: {
            label: 'Expiry',
          },
          cvn: {
            label: 'Cvn',
          },
          name: {
            label: 'Name',
            hide: true,
          },
        },
        clientsideValidation: true,
        disableSubmitButtonOnClick: true,
        callback: function (code: string, data: any) {
          if (code === 'success') {
            setLoading(true);
            paymentProcess({
              authKey: payData.authKey,
              businessNo: bizNo,
              saleOrgId: saleOrgId,
            })
              .then((res) => {
                if (res) {
                  message.success('支付成功');
                  onCancel?.();
                  const timer = setTimeout(() => {
                    onSuccess?.();
                    clearTimeout(timer);
                  }, 1000);
                }
              })
              .finally(() => {
                setLoading(false);
              });
          }
        },
      });
    }
  }, [payData, bizNo, saleOrgId, onCancel, onSuccess]);

  return (
    <Spin spinning={loading}>
      <div id="cardForm" />
    </Spin>
  );
}
