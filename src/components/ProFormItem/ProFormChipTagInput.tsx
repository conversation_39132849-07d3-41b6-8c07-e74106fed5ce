import React from 'react';
import { Tag, Input, message } from 'antd';

interface ChipInputProps {
  value?: string;
  onChange?: (val: string) => void;
}

const ProFormChipTagInput: React.FC<ChipInputProps> = ({ value = '', onChange }) => {
  const chips = React.useMemo(() => {
    return value
      ? value
          .split(',')
          .map((c) => c.trim())
          .filter(Boolean)
      : [];
  }, [value]);

  const [inputValue, setInputValue] = React.useState('');

  const handleAdd = () => {
    if (!inputValue.trim()) return;
    if (chips.length >= 10) {
      message.warning('达到上限');
      return;
    }
    const next = [...chips, inputValue.trim()];
    onChange?.(next.join(',')); // 回调用 string
    setInputValue('');
  };

  const handleRemove = (index: number) => {
    const next = chips.filter((_, i) => i !== index);
    onChange?.(next.join(','));
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      handleAdd();
    } else if (e.key === 'Backspace' && inputValue === '' && chips.length > 0) {
      handleRemove(chips.length - 1);
    }
  };

  console.log('chips', chips);

  return (
    <div className="flex flex-wrap items-center border border-solid border-[#d9d9d9] rounded-sm px-2">
      {chips.map((chip, index) => (
        <Tag
          closable
          onClose={(e) => {
            e.preventDefault();
            handleRemove(index);
          }}
        >
          {chip}
        </Tag>
      ))}
      <Input
        style={{ flex: 1, paddingLeft: 2 }}
        value={inputValue}
        placeholder={chips.length >= 10 ? '达到上限' : '输入后按回车添加车牌'}
        onChange={(e) => setInputValue(e.target.value)}
        onKeyDown={handleKeyDown}
        onBlur={() => handleAdd()}
        variant="borderless"
      />
    </div>
  );
};

export default ProFormChipTagInput;
