export interface ActivityItemEntity {
  /**
   * 活动列表
   */
  activityList?: ActivityList[];
  /**
   * 商品ID
   */
  itemId?: string;
}

export interface ActivityList {
  /**
   * 活动id
   */
  activityId?: number;
  /**
   * 套餐商品列表
   */
  activityItems?: ActivityItem[];
  /**
   * 活动名称
   */
  activityName?: string;
  /**
   * 活动标签
   */
  activityTag?: string;
  /**
   * 活动类型
   */
  activityType?: number;
  /**
   * 购买本品数量（仅买赠活动使用）
   */
  buySelfNum?: number;
  /**
   * 满赠阶梯信息
   */
  fullGiveRules?: FullGiveRule[];
  /**
   * 赠送本品数量（仅买赠活动使用）
   */
  giftSelfNum?: number;
  /**
   * 套餐划线价
   */
  originalTotalPrice?: number;
  /**
   * 含税套餐划线价
   */
  originalTotalPriceTax?: number;
  /**
   * 活动价，用于特价活动计算划线价
   */
  promotionPrice?: number;
  /**
   * 含税活动价，用于特价活动计算划线价
   */
  promotionPriceTax?: number;
  /**
   * 套餐总价
   */
  promotionTotalPrice?: number;
  /**
   * 含税套餐总价
   */
  promotionTotalPriceTax?: number;
}

export interface ActivityItem {
  /**
   * 活动id编辑时必填
   */
  activityId?: number;
  /**
   * 品牌id
   */
  brandId?: number;
  /**
   * 品牌名称
   */
  brandName?: string;
  /**
   * 购买本品数量（仅买赠活动使用）
   */
  buySelfNum?: number;
  /**
   * 商品类目id
   */
  categoryId?: number;
  /**
   * 商品类目名称
   */
  categoryName?: string;
  /**
   * 赠送本品数量（仅买赠活动使用）
   */
  giftSelfNum?: number;
  /**
   * 赠品总限购（仅买赠活动使用）
   */
  giftTotalMaxBuyNum?: number;
  /**
   * 商品图片
   */
  imageUrl?: string;
  /**
   * 商品id
   */
  itemId?: string;
  /**
   * 商品名称
   */
  itemName?: string;
  /**
   * itemSn
   */
  itemSn?: string;
  /**
   * 单客户最大购买数量(限购数量)
   */
  maxBuyNum?: number;
  /**
   * 单客户最小购买数量(起购数量)
   */
  minBuyNum?: number;
  /**
   * 商品数量（仅组合套餐使用）
   */
  num?: number;
  /**
   * 活动价单位元
   */
  promotionPrice?: number;
  /**
   * 显示顺序
   */
  showOrder?: number;
  /**
   * 商品售价单位元
   */
  suggestPrice?: number;
  /**
   * 总最大购买数量(活动商品总限购)
   */
  totalMaxBuyNum?: number;
}

export interface FullGiveRule {
  /**
   * 阶梯赠品
   */
  activityGiftItems?: ActivityGiftItem[];
  /**
   * 活动id
   */
  activityId?: number;
  /**
   * 满足件数或元，满20元or满20件
   */
  enableAmount?: string;
  /**
   * 赠送规则类型满元OR满件1满元，2满件
   */
  giftRuleType?: number;
  /**
   * 阶梯
   */
  ladder?: number;
}

export interface ActivityGiftItem {
  /**
   * 活动id编辑时必填
   */
  activityId?: number;
  /**
   * 品牌id
   */
  brandId?: number;
  /**
   * 品牌名称
   */
  brandName?: string;
  /**
   * 商品类目id
   */
  categoryId?: number;
  /**
   * 商品类目名称
   */
  categoryName?: string;
  /**
   * 赠送数量
   */
  giftNum?: number;
  /**
   * 赠品总限购数量
   */
  giftTotalMaxBuyNum?: number;
  /**
   * 商品图片
   */
  imageUrl?: string;
  /**
   * 商品id
   */
  itemId?: string;
  /**
   * 商品名称
   */
  itemName?: string;
  /**
   * itemSn
   */
  itemSn?: string;
  /**
   * 商品售价单位元
   */
  suggestPrice?: number;
}
