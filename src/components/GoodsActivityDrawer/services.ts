import { request } from '@/utils/request';
import { ActivityItemEntity } from './types/activity.item.entity';
import { QueryActivityItemListRequest } from './types/query.activity.item.list.request';

/**
 * 查询商品的活动信息
 */
export const queryActivityItemList = async (params: QueryActivityItemListRequest) => {
  return request<ActivityItemEntity>(`/ipmspromotion/activityQuery/queryActivityItemList`, {
    data: params,
  });
};

/**
 * 商城免登
 */
export const autoLogin = async (cstId: string) => {
  return request<{ dSessionId: string }>(`/ipmspassport/ecommerceLoginFacade/autoLogin`, {
    data: { cstId },
  });
};
