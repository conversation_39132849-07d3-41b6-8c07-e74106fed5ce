// 返回 日/月/年

import moment from 'moment';

const getDateFormat = (date: string | number | Date, format: string) => {
  return moment(date).format(format);
};

export const TimeFormat = (props: { time: string | number | Date, showTime?: boolean }) => {
  const { time } = props;
  if (!time) return <span>-</span>;
  return <span>{getDateFormat(time, props.showTime ? 'DD/MM/YYYY HH:mm:ss' : 'DD/MM/YYYY')}</span>;
};


export const TimeRangeFormat = (props: { startTime: string | number | Date, endTime: string | number | Date, shwoTime?: boolean }) => {
  const { startTime, endTime, shwoTime } = props;
  if (!startTime || !endTime) return <span>-</span>;
  return <span>{getDateFormat(startTime, shwoTime ? 'DD/MM/YYYY HH:mm:ss' : 'DD/MM/YYYY')} - {getDateFormat(endTime, shwoTime ? 'DD/MM/YYYY HH:mm:ss' : 'DD/MM/YYYY')}</span>;
};